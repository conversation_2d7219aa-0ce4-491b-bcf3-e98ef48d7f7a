import React, { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Calendar, MapPin, Building, Users, TrendingUp, Award, Code, Briefcase } from 'lucide-react';
import { xpCareerMilestones } from '../../shared/data/xpAnalyticsData';

interface TimelineTooltipProps {
  milestone: typeof xpCareerMilestones[0];
  isVisible: boolean;
  position: { x: number; y: number };
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const TimelineTooltip: React.FC<TimelineTooltipProps> = ({
  milestone,
  isVisible,
  position,
  onMouseEnter,
  onMouseLeave
}) => {
  if (!isVisible) return null;

  return (
    <div
      className="fixed z-50 w-80 p-4 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 backdrop-blur-xl"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)',
        marginTop: '-10px'
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="space-y-3">
        <div className="flex items-center gap-3">
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
            style={{ backgroundColor: milestone.companyColor }}
          >
            {milestone.xpIcon}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">{milestone.position}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">{milestone.company}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.durationMonths} months</span>
          </div>
          <div className="flex items-center gap-1">
            <MapPin className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.location}</span>
          </div>
          <div className="flex items-center gap-1">
            <Building className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.industry}</span>
          </div>
          <div className="flex items-center gap-1">
            <Briefcase className="w-3 h-3 text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">{milestone.employmentType}</span>
          </div>
        </div>

        <div>
          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Technologies</h4>
          <div className="flex flex-wrap gap-1">
            {milestone.technologiesUsed.slice(0, 6).map((tech, index) => (
              <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                {tech}
              </Badge>
            ))}
            {milestone.technologiesUsed.length > 6 && (
              <Badge variant="outline" className="text-xs px-2 py-0.5">
                +{milestone.technologiesUsed.length - 6}
              </Badge>
            )}
          </div>
        </div>

        {/* Key Responsibilities */}
        {milestone.responsibilities && milestone.responsibilities.length > 0 && (
          <div>
            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Responsibilities</h4>
            <div className="space-y-1">
              {milestone.responsibilities.slice(0, 3).map((responsibility, index) => (
                <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                  • {responsibility}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Project Types */}
        {milestone.keyProjects && milestone.keyProjects.length > 0 && (
          <div>
            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Project Types</h4>
            <div className="space-y-1">
              {milestone.keyProjects.slice(0, 2).map((project, index) => (
                <div key={index} className="text-xs">
                  <p className="font-medium text-gray-800 dark:text-gray-200">{project.name}</p>
                  <p className="text-gray-600 dark:text-gray-400">{project.description}</p>
                </div>
              ))}
              {milestone.keyProjects.length > 2 && (
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  +{milestone.keyProjects.length - 2} more projects
                </p>
              )}
            </div>
          </div>
        )}

        {/* Career Progression */}
        <div>
          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Career Level</h4>
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="text-xs"
              style={{ borderColor: milestone.companyColor, color: milestone.companyColor }}
            >
              {milestone.careerLevel}
            </Badge>
            <span className="text-xs text-gray-600 dark:text-gray-400">
              {milestone.durationMonths} months
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

const EnhancedCareerTimeline: React.FC = () => {
  const [hoveredMilestone, setHoveredMilestone] = useState<string | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  // Sort milestones by start date
  const sortedMilestones = [...xpCareerMilestones].sort((a, b) =>
    new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
  );

  // Calculate timeline positions
  const timelineStart = new Date(sortedMilestones[0].startDate);
  const timelineEnd = new Date();
  const totalDuration = timelineEnd.getTime() - timelineStart.getTime();

  const handleMilestoneHover = (milestoneId: string, event: React.MouseEvent) => {
    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }

    setHoveredMilestone(milestoneId);
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top
    });
  };

  const handleMilestoneLeave = () => {
    // Add a small delay before hiding to prevent flickering
    const timeout = setTimeout(() => {
      setHoveredMilestone(null);
    }, 100);
    setHoverTimeout(timeout);
  };

  const handleTooltipEnter = () => {
    // Clear timeout if user hovers over tooltip
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
  };

  const handleTooltipLeave = () => {
    setHoveredMilestone(null);
  };

  // Create technology adoption timeline data
  const technologyTimeline = sortedMilestones.reduce((acc, milestone) => {
    milestone.technologiesLearned.forEach(tech => {
      if (!acc[tech]) {
        acc[tech] = {
          technology: tech,
          adoptedAt: milestone.company,
          adoptedDate: milestone.startDate,
          color: milestone.companyColor,
          category: milestone.industry
        };
      }
    });
    return acc;
  }, {} as Record<string, { technology: string; adoptedAt: string; adoptedDate: string; color: string; category: string }>);

  const technologyTimelineArray = Object.values(technologyTimeline).sort((a, b) =>
    new Date(a.adoptedDate).getTime() - new Date(b.adoptedDate).getTime()
  );

  return (
    <div className="w-full space-y-12">
      {/* Timeline Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-light text-gray-900 dark:text-white mb-2">
          Career Journey Timeline
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Comprehensive professional journey showcasing all {sortedMilestones.length} career positions spanning {Math.round(totalDuration / (1000 * 60 * 60 * 24 * 365))} years of experience
        </p>
      </div>

      {/* Timeline Container */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute top-16 left-0 right-0 h-1 bg-gradient-to-r from-blue-200 via-green-200 to-orange-200 dark:from-blue-800 dark:via-green-800 dark:to-orange-800 rounded-full"></div>

        {/* Timeline Milestones - Responsive Layout */}
        <div className="relative hidden md:flex justify-between items-start min-h-[250px]">
          {sortedMilestones.map((milestone, index) => {
            const startDate = new Date(milestone.startDate);
            const endDate = new Date(milestone.endDate);
            const position = ((startDate.getTime() - timelineStart.getTime()) / totalDuration) * 100;
            const width = ((endDate.getTime() - startDate.getTime()) / totalDuration) * 100;

            return (
              <div
                key={milestone.id}
                className="absolute flex flex-col items-center"
                style={{ left: `${position}%`, width: `${Math.max(width, 8)}%` }}
                onMouseEnter={(e) => handleMilestoneHover(milestone.id, e)}
                onMouseLeave={handleMilestoneLeave}
              >
                {/* Company Node with Name and Years */}
                <div className="relative z-10 mb-4 flex flex-col items-center">
                  {/* Company Name - Top Half */}
                  <div className="text-center mb-2">
                    <h3 className="font-semibold text-sm text-gray-900 dark:text-white mb-1">
                      {milestone.company}
                    </h3>
                    {/* Years Enumeration */}
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {(() => {
                        const startYear = new Date(milestone.startDate).getFullYear();
                        const endYear = new Date(milestone.endDate).getFullYear();

                        if (startYear === endYear) {
                          return `${startYear}`;
                        }

                        const years = [];
                        for (let year = startYear; year <= endYear; year++) {
                          years.push(year.toString());
                        }

                        return years.join(', ');
                      })()}
                    </div>
                  </div>

                  {/* Company Icon Node */}
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg cursor-pointer transform transition-all duration-300 hover:scale-110 hover:shadow-xl"
                    style={{ backgroundColor: milestone.companyColor }}
                  >
                    {milestone.xpIcon}
                  </div>

                  {/* Duration Bar */}
                  <div
                    className="absolute top-full mt-2 h-2 rounded-full opacity-70"
                    style={{
                      backgroundColor: milestone.companyColor,
                      width: '100%',
                      minWidth: '60px'
                    }}
                  ></div>

                  {/* Achievement Badge */}
                  {milestone.keyProjects && milestone.keyProjects.length > 0 && (
                    <div className="absolute -top-2 -right-2">
                      <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center shadow-lg">
                        <Award className="w-3 h-3 text-white" />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Timeline Labels */}
        <div className="hidden md:flex justify-between mt-8 text-sm text-gray-500 dark:text-gray-400">
          <span>{timelineStart.getFullYear()}</span>
          <span>Present</span>
        </div>

        {/* Mobile Timeline - Vertical Layout */}
        <div className="md:hidden space-y-6 mt-8">
          {sortedMilestones.map((milestone, index) => (
            <div key={milestone.id} className="relative flex gap-4">
              {/* Vertical Line */}
              {index < sortedMilestones.length - 1 && (
                <div className="absolute left-6 top-12 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"></div>
              )}

              {/* Company Node */}
              <div className="flex-shrink-0">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-lg relative"
                  style={{ backgroundColor: milestone.companyColor }}
                >
                  {milestone.xpIcon}
                  {milestone.keyProjects && milestone.keyProjects.length > 0 && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-4 h-4 rounded-full bg-yellow-500 flex items-center justify-center">
                        <Award className="w-2 h-2 text-white" />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Company Info */}
              <Card className="flex-1 border-0 bg-white/90 dark:bg-white/10 backdrop-blur-xl rounded-xl shadow-lg">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div>
                      <h3 className="font-semibold text-sm text-gray-900 dark:text-white">
                        {milestone.position}
                      </h3>
                      <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                        {milestone.company}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {milestone.duration}
                      </p>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      <Badge
                        variant="outline"
                        className="text-xs"
                        style={{ borderColor: milestone.companyColor, color: milestone.companyColor }}
                      >
                        {milestone.careerLevel}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {milestone.employmentType}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {milestone.technologiesLearned.slice(0, 3).map((tech, i) => (
                        <Badge key={i} variant="secondary" className="text-xs px-1.5 py-0.5">
                          {tech}
                        </Badge>
                      ))}
                    </div>

                    {milestone.keyProjects && milestone.keyProjects.length > 0 && (
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        <strong>Key Project:</strong> {milestone.keyProjects[0].name}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Technology Adoption Timeline */}
      <div className="mt-16">
        <div className="mb-6">
          <h3 className="text-xl font-light text-gray-900 dark:text-white mb-2 flex items-center gap-2">
            <Code className="w-5 h-5" />
            Technology Adoption Journey
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Key technologies learned throughout career progression ({technologyTimelineArray.length} technologies)
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {technologyTimelineArray.map((tech, index) => (
            <Card key={tech.technology} className="border-0 bg-white/90 dark:bg-white/10 backdrop-blur-xl rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
                    style={{ backgroundColor: tech.color }}
                  >
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-sm text-gray-900 dark:text-white">
                      {tech.technology}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Adopted at {tech.adoptedAt}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {tech.category}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tooltip */}
      {hoveredMilestone && (
        <TimelineTooltip
          milestone={sortedMilestones.find(m => m.id === hoveredMilestone)!}
          isVisible={!!hoveredMilestone}
          position={tooltipPosition}
          onMouseEnter={handleTooltipEnter}
          onMouseLeave={handleTooltipLeave}
        />
      )}
    </div>
  );
};

export default EnhancedCareerTimeline;
