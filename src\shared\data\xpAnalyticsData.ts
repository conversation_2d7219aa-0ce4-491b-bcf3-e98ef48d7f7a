/**
 * XP Analytics Data Structure
 * Professional portfolio data optimized for Windows XP theme visualization
 */

// XP Color Palette for consistent theming
export const xpColorScheme = {
  primary: "#0078D4",      // XP Blue
  secondary: "#316AC5",    // Darker XP Blue
  accent: "#FFD700",       // XP Yellow
  success: "#107C10",      // XP Green
  warning: "#FF8C00",      // XP Orange
  background: "#F0F0F0",   // XP Gray
  border: "#808080",       // XP Border Gray
  white: "#FFFFFF",
  text: "#000000"
};

// Skills data with XP-compatible styling and proficiency ratings
export const xpSkillsData = [
  // Backend Technologies
  { name: "C#", category: "backend", proficiency: 9, years: 10, xpColor: xpColorScheme.primary, lastUsed: "2024-02" },
  { name: "VB.NET", category: "backend", proficiency: 8, years: 8, xpColor: xpColorScheme.secondary, lastUsed: "2023-12" },
  { name: "ASP.NET", category: "backend", proficiency: 9, years: 10, xpColor: xpColorScheme.primary, lastUsed: "2024-02" },
  { name: ".NET Core", category: "backend", proficiency: 8, years: 5, xpColor: xpColorScheme.primary, lastUsed: "2024-02" },
  { name: "Node.js", category: "backend", proficiency: 7, years: 3, xpColor: xpColorScheme.success, lastUsed: "2024-01" },

  // Frontend Technologies
  { name: "React", category: "frontend", proficiency: 8, years: 4, xpColor: "#61DAFB", lastUsed: "2024-02" },
  { name: "Angular", category: "frontend", proficiency: 7, years: 3, xpColor: "#DD0031", lastUsed: "2023-11" },
  { name: "TypeScript", category: "frontend", proficiency: 8, years: 4, xpColor: "#3178C6", lastUsed: "2024-02" },
  { name: "JavaScript", category: "frontend", proficiency: 9, years: 10, xpColor: "#F7DF1E", lastUsed: "2024-02" },
  { name: "jQuery", category: "frontend", proficiency: 8, years: 8, xpColor: "#0769AD", lastUsed: "2023-10" },

  // Database Technologies
  { name: "MSSQL", category: "database", proficiency: 9, years: 10, xpColor: xpColorScheme.warning, lastUsed: "2024-02" },
  { name: "MySQL", category: "database", proficiency: 8, years: 6, xpColor: "#4479A1", lastUsed: "2023-12" },
  { name: "PostgreSQL", category: "database", proficiency: 7, years: 3, xpColor: "#336791", lastUsed: "2023-09" },
  { name: "MongoDB", category: "database", proficiency: 6, years: 2, xpColor: "#47A248", lastUsed: "2023-08" },
  { name: "ElasticSearch", category: "database", proficiency: 7, years: 2, xpColor: "#005571", lastUsed: "2024-01" },

  // Development Tools
  { name: "Visual Studio", category: "tools", proficiency: 9, years: 10, xpColor: "#5C2D91", lastUsed: "2024-02" },
  { name: "VS Code", category: "tools", proficiency: 9, years: 6, xpColor: "#007ACC", lastUsed: "2024-02" },
  { name: "Docker", category: "tools", proficiency: 7, years: 3, xpColor: "#2496ED", lastUsed: "2024-01" },
  { name: "Azure DevOps", category: "tools", proficiency: 8, years: 4, xpColor: "#0078D4", lastUsed: "2024-02" },
  { name: "Postman", category: "tools", proficiency: 8, years: 5, xpColor: "#FF6C37", lastUsed: "2024-02" }
];

// Enhanced career milestones with comprehensive timeline data - Complete Career History
export const xpCareerMilestones = [
  {
    id: "lbh-digital",
    date: "2023-03",
    startDate: "2023-03-01",
    endDate: "2024-02-29",
    company: "LBH Digital",
    position: "Senior Software Developer",
    location: "Australia (Remote)",
    xpIcon: "🏢",
    duration: "Mar 2023 - Feb 2024",
    durationMonths: 12,
    companyColor: "#2563EB", // Blue for enterprise
    companySize: "Medium (50-200 employees)",
    industry: "Financial Technology",
    employmentType: "Full-time Contract",
    careerLevel: "Senior",
    achievements: [
      "Involved in maintenance and creating new features for the company's web platform for loans management (Kubio Datasage)",
      "Optimized and accelerated the company's CV/excel data extraction process through refactoring and streamlining the extraction methods resulting in significant time and resource savings",
      "Employed ElasticSearch for storage of parsed files and data management"
    ],
    responsibilities: [
      "Full-stack development using C# and ASP.NET",
      "Database optimization and query performance tuning",
      "ElasticSearch implementation and data indexing",
      "Code review and technical documentation"
    ],
    technologiesUsed: ["C#", "ASP.NET", "MSSQL", "ElasticSearch", "JavaScript", "HTML5", "CSS3"],
    technologiesLearned: ["ElasticSearch", "Advanced SQL Optimization"],
    impactMetrics: [
      { metric: "Performance Improvement", value: 40, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Data Processing Speed", value: 60, unit: "% faster", xpColor: xpColorScheme.success },
      { metric: "System Reliability", value: 95, unit: "% uptime", xpColor: xpColorScheme.primary },
      { metric: "Code Coverage", value: 85, unit: "%", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Kubio Datasage Platform Enhancement",
        description: "Enhanced loan management platform with improved data processing",
        technologies: ["C#", "ASP.NET", "MSSQL", "ElasticSearch"],
        impact: "40% performance improvement"
      }
    ]
  },
  {
    id: "clericalsoft-solutions",
    date: "2023-04",
    startDate: "2023-04-01",
    endDate: "2023-08-31",
    company: "Clericalsoft Solutions",
    position: "Senior Software Developer",
    location: "Davao City, Philippines",
    xpIcon: "🔍",
    duration: "Apr 2023 - Aug 2023",
    durationMonths: 5,
    companyColor: "#8B5CF6", // Purple for SEO/Marketing tech
    companySize: "Small (10-50 employees)",
    industry: "SEO & Marketing Technology",
    employmentType: "Full-time Contract",
    careerLevel: "Senior",
    achievements: [
      "Keyword Chef (US) - collaborated on SEO software projects including Keyword Finder web app used by SEOs and bloggers",
      "Niche Finder - developed tools used by bloggers, SEOs and PPC advertisers",
      "Velotio Technologies (India) - collaborated on Jobility, a job/gig matching platform built in ReactJs and .Net"
    ],
    responsibilities: [
      "SEO software development and optimization",
      "React.js frontend development",
      ".NET backend development",
      "Cross-team collaboration with international clients"
    ],
    technologiesUsed: ["C#", ".NET", "React", "JavaScript", "TypeScript", "MSSQL"],
    technologiesLearned: ["SEO Analytics", "Marketing Technology", "International Collaboration"],
    impactMetrics: [
      { metric: "SEO Tools Delivered", value: 3, unit: "platforms", xpColor: xpColorScheme.primary },
      { metric: "User Engagement", value: 85, unit: "% increase", xpColor: xpColorScheme.success },
      { metric: "Client Satisfaction", value: 95, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Platform Performance", value: 30, unit: "% improved", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Keyword Chef Platform",
        description: "SEO keyword research and analysis tool for content creators",
        technologies: ["React", ".NET", "JavaScript"],
        impact: "Enhanced SEO workflow for bloggers"
      },
      {
        name: "Jobility Platform",
        description: "Job and gig matching platform with advanced filtering",
        technologies: ["React", ".NET", "TypeScript"],
        impact: "Streamlined job matching process"
      }
    ]
  },
  {
    id: "infivex-teqto",
    date: "2022-10",
    startDate: "2022-10-01",
    endDate: "2023-06-30",
    company: "Infivex | Teqto",
    position: "Senior Software Developer",
    location: "Belgium (Remote)",
    xpIcon: "🏭",
    duration: "Oct 2022 - Jun 2023",
    durationMonths: 9,
    companyColor: "#EF4444", // Red for manufacturing/industrial
    companySize: "Medium (50-200 employees)",
    industry: "Manufacturing & ERP Solutions",
    employmentType: "Full-time Contract",
    careerLevel: "Senior",
    achievements: [
      "Involved in design, maintenance and creating new features for company's software clients with integration to Exact Online ERP Suite",
      "Trafiroad - developed web-app for tracking sales processes and assembly-line activity flow for road signages and equipments",
      "Beyers Plastics - created web-app for tracking sales processes and cleanroom processes for medical-grade plastics used by pharmaceutical companies in the Netherlands"
    ],
    responsibilities: [
      "ERP system integration and development",
      "Manufacturing process tracking systems",
      "Client-specific software customization",
      "Quality assurance and compliance systems"
    ],
    technologiesUsed: ["C#", "ASP.NET", "Exact Online API", "MSSQL", "JavaScript", "HTML5", "CSS3"],
    technologiesLearned: ["Exact Online ERP", "Manufacturing Systems", "Cleanroom Compliance"],
    impactMetrics: [
      { metric: "ERP Integrations", value: 2, unit: "major clients", xpColor: xpColorScheme.primary },
      { metric: "Process Efficiency", value: 45, unit: "% improved", xpColor: xpColorScheme.success },
      { metric: "Compliance Rate", value: 100, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Client Retention", value: 100, unit: "%", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Trafiroad Management System",
        description: "Sales and assembly-line tracking for road signage manufacturing",
        technologies: ["C#", "ASP.NET", "Exact Online API"],
        impact: "Streamlined manufacturing workflow"
      },
      {
        name: "Beyers Plastics Quality System",
        description: "Cleanroom process tracking for medical-grade plastics",
        technologies: ["C#", "ASP.NET", "MSSQL"],
        impact: "100% compliance with pharmaceutical standards"
      }
    ]
  },
  {
    id: "cashjar-payment-hq",
    date: "2019-10",
    startDate: "2019-10-01",
    endDate: "2022-10-31",
    company: "Cashjar | Payment HQ",
    position: "Senior Software Developer",
    location: "United States (Remote)",
    xpIcon: "💳",
    duration: "Oct 2019 - Oct 2022",
    durationMonths: 37,
    companyColor: "#10B981", // Green for fintech
    companySize: "Medium (50-200 employees)",
    industry: "Financial Technology",
    employmentType: "Full-time",
    careerLevel: "Senior",
    achievements: [
      "Involved in maintenance and adding new features for the company's financial software and in-house apps",
      "Handled daily monitoring of system activities using Azure Application Insights, Log Analytics and troubleshooting",
      "Provided night-shift support for system's, technical teams' and US-based users' post-deployment reports and feedback",
      "Managed 4 software projects: Merchant Admin, Merchant Portal, Cashjar Port, and Cashjar Mobile (Nativescript)",
      "Developed DB Migration Tool for rapid update/syncing of databases across environments"
    ],
    responsibilities: [
      "Financial software development and maintenance",
      "Azure cloud monitoring and troubleshooting",
      "Multi-project management and coordination",
      "Database migration and synchronization",
      "24/7 production support"
    ],
    technologiesUsed: ["C#", ".NET", "Azure", "MSSQL", "NativeScript", "JavaScript", "Application Insights"],
    technologiesLearned: ["Azure Application Insights", "Financial Systems", "Mobile Development", "DevOps"],
    impactMetrics: [
      { metric: "Projects Managed", value: 4, unit: "concurrent", xpColor: xpColorScheme.primary },
      { metric: "System Uptime", value: 99.9, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Migration Efficiency", value: 80, unit: "% faster", xpColor: xpColorScheme.success },
      { metric: "Support Response", value: 15, unit: "min avg", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Merchant Management Suite",
        description: "Comprehensive merchant admin and portal applications",
        technologies: ["C#", ".NET", "Azure", "MSSQL"],
        impact: "Streamlined merchant operations"
      },
      {
        name: "Database Migration Tool",
        description: "Automated tool for database synchronization across environments",
        technologies: ["C#", ".NET", "MSSQL"],
        impact: "80% reduction in migration time"
      }
    ]
  },
  {
    id: "itmaskinen-consulting",
    date: "2019-04",
    startDate: "2019-04-01",
    endDate: "2019-10-31",
    company: "Itmaskinen Consulting AB",
    position: "Software Developer",
    location: "Sweden (Remote)",
    xpIcon: "🏢",
    duration: "Apr 2019 - Oct 2019",
    durationMonths: 7,
    companyColor: "#3B82F6", // Blue for consulting
    companySize: "Small (10-50 employees)",
    industry: "Software Consulting",
    employmentType: "Full-time Contract",
    careerLevel: "Mid-level to Senior",
    achievements: [
      "Involved in designing software for the company's clients",
      "TimeTracker (in-house) - developed tool used by company employees for time and record-keeping of tasks and projects",
      "NBS (client) - created CRM software for organizing Events",
      "BIM Ctrl (client) - built Property Assessment / Security Management software"
    ],
    responsibilities: [
      "Client software design and development",
      "Time tracking system development",
      "CRM and event management systems",
      "Property assessment and security software"
    ],
    technologiesUsed: ["C#", ".NET Framework", "MSSQL", "JavaScript", "HTML5", "CSS3"],
    technologiesLearned: ["CRM Development", "Property Management Systems", "Time Tracking"],
    impactMetrics: [
      { metric: "Client Projects", value: 3, unit: "delivered", xpColor: xpColorScheme.primary },
      { metric: "Time Tracking Efficiency", value: 60, unit: "% improved", xpColor: xpColorScheme.success },
      { metric: "Client Satisfaction", value: 92, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Project Delivery", value: 100, unit: "% on time", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "TimeTracker System",
        description: "Internal time and project tracking tool for consultants",
        technologies: ["C#", ".NET Framework", "MSSQL"],
        impact: "Improved project time management"
      },
      {
        name: "BIM Ctrl Security Platform",
        description: "Property assessment and security management system",
        technologies: ["C#", ".NET Framework", "JavaScript"],
        impact: "Enhanced property security management"
      }
    ]
  },
  {
    id: "datasoftlogic-corp",
    date: "2014-10",
    startDate: "2014-10-01",
    endDate: "2018-11-30",
    company: "DATASOFTLOGIC CORP",
    position: "Software Developer",
    location: "Davao City, Philippines",
    xpIcon: "🏥",
    duration: "Oct 2014 - Nov 2018",
    durationMonths: 50,
    companyColor: "#059669", // Green for healthcare
    companySize: "Medium (50-200 employees)",
    industry: "Healthcare Technology",
    employmentType: "Full-time",
    careerLevel: "Mid-level",
    achievements: [
      "Part of development team for the company's Health Care System (Hospice WebApp) used by Hospice agencies in the United States",
      "Focused on development of new features, bug fixing and system improvements",
      "Worked with healthcare-specific requirements and compliance standards"
    ],
    responsibilities: [
      "Healthcare system development and maintenance",
      "Feature development and bug resolution",
      "Compliance with healthcare regulations",
      "System performance optimization"
    ],
    technologiesUsed: ["C#", "VB.NET", ".NET Framework", "MSSQL", "JavaScript", "Crystal Reports"],
    technologiesLearned: ["Healthcare Systems", "HIPAA Compliance", "Medical Data Management"],
    impactMetrics: [
      { metric: "System Reliability", value: 98, unit: "% uptime", xpColor: xpColorScheme.primary },
      { metric: "Feature Delivery", value: 25, unit: "+ features", xpColor: xpColorScheme.success },
      { metric: "Bug Resolution", value: 95, unit: "% within SLA", xpColor: xpColorScheme.success },
      { metric: "Compliance Rate", value: 100, unit: "%", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Hospice Management System",
        description: "Comprehensive healthcare management platform for US hospice agencies",
        technologies: ["C#", "VB.NET", ".NET Framework", "MSSQL"],
        impact: "Streamlined hospice care operations"
      }
    ]
  },
  {
    id: "carve-business-management",
    date: "2014-02",
    startDate: "2014-02-01",
    endDate: "2014-05-31",
    company: "CARVE BUSINESS MANAGEMENT AND SERVICES",
    position: "Software Developer",
    location: "Davao City, Philippines",
    xpIcon: "📦",
    duration: "Feb 2014 - May 2014",
    durationMonths: 4,
    companyColor: "#F59E0B", // Orange for business management
    companySize: "Small (10-50 employees)",
    industry: "Business Management Software",
    employmentType: "Full-time",
    careerLevel: "Junior to Mid-level",
    achievements: [
      "Re-developed, enhanced, and translated legacy code (to .NET) of the Inventory System previously built on PHP, MySQL",
      "Started initial development of the company's in-house Human Resource and Payroll System"
    ],
    responsibilities: [
      "Legacy system migration and modernization",
      "Inventory management system development",
      "HR and payroll system development",
      "Code translation and enhancement"
    ],
    technologiesUsed: ["C#", ".NET Framework", "MSSQL", "PHP", "MySQL"],
    technologiesLearned: ["Legacy System Migration", "HR Systems", "Payroll Processing"],
    impactMetrics: [
      { metric: "System Migration", value: 100, unit: "% complete", xpColor: xpColorScheme.primary },
      { metric: "Performance Improvement", value: 50, unit: "%", xpColor: xpColorScheme.success },
      { metric: "Code Quality", value: 85, unit: "% improved", xpColor: xpColorScheme.success },
      { metric: "Development Speed", value: 40, unit: "% faster", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Inventory System Migration",
        description: "Legacy PHP/MySQL system migrated to .NET/MSSQL",
        technologies: ["C#", ".NET Framework", "MSSQL"],
        impact: "Modernized inventory management"
      },
      {
        name: "HR & Payroll System",
        description: "Initial development of human resource and payroll management",
        technologies: ["C#", ".NET Framework", "MSSQL"],
        impact: "Automated HR processes"
      }
    ]
  },
  {
    id: "iekzeed-software",
    date: "2013-06",
    startDate: "2013-06-01",
    endDate: "2014-10-31",
    company: "i.ekZeed SOFTWARE DESIGN AND DEVELOPMENT CO",
    position: "Team Lead | Software Developer",
    location: "Davao City, Philippines",
    xpIcon: "👥",
    duration: "Jun 2013 - Oct 2014",
    durationMonths: 17,
    companyColor: "#8B5CF6", // Purple for leadership role
    companySize: "Small (10-50 employees)",
    industry: "Custom Software Development",
    employmentType: "Full-time",
    careerLevel: "Junior to Mid-level",
    achievements: [
      "Led a team of 4 junior developers in developing the company's customizable ERP system",
      "Maintained and added new features specific to client requests tailored to their business needs",
      "Developed web app version of the desktop software",
      "Managed software solutions for clients including MyGas Petroleum Corp, Goodyear/Mike Servitek, NexGear Autoshop, A1 Tires And Battery Supplies, DCounter Hotel, and Rhodwill Construction"
    ],
    responsibilities: [
      "Team leadership and mentoring",
      "ERP system development and customization",
      "Client requirement analysis and implementation",
      "Desktop to web application migration",
      "Multi-client project management"
    ],
    technologiesUsed: ["C#", "VB.NET", ".NET Framework", "MSSQL", "JavaScript", "Crystal Reports"],
    technologiesLearned: ["Team Leadership", "ERP Systems", "Client Management", "Web Development"],
    impactMetrics: [
      { metric: "Team Members Led", value: 4, unit: "developers", xpColor: xpColorScheme.primary },
      { metric: "Client Projects", value: 6, unit: "+ major clients", xpColor: xpColorScheme.success },
      { metric: "System Customizations", value: 20, unit: "+ features", xpColor: xpColorScheme.success },
      { metric: "Client Retention", value: 95, unit: "%", xpColor: xpColorScheme.warning }
    ],
    keyProjects: [
      {
        name: "Customizable ERP System",
        description: "Flexible ERP solution tailored to various business needs",
        technologies: ["C#", "VB.NET", ".NET Framework", "MSSQL"],
        impact: "Served 6+ major clients across industries"
      },
      {
        name: "Desktop to Web Migration",
        description: "Converted desktop ERP to web-based application",
        technologies: ["C#", ".NET Framework", "JavaScript"],
        impact: "Enhanced accessibility and usability"
      }
    ]
  }
];

// Technology evolution timeline showing learning progression across complete career
export const xpTechnologyEvolution = [
  { year: 2013, technologies: ["C#", "VB.NET", ".NET Framework", "MSSQL", "Visual Studio"], level: 2 },
  { year: 2014, technologies: ["Crystal Reports", "Legacy Migration", "PHP to .NET"], level: 3 },
  { year: 2015, technologies: ["Healthcare Systems", "HIPAA Compliance", "JavaScript"], level: 4 },
  { year: 2017, technologies: ["ASP.NET", "jQuery", "HTML5", "CSS3"], level: 5 },
  { year: 2019, technologies: ["CRM Development", "Property Management", "Time Tracking"], level: 6 },
  { year: 2020, technologies: ["Azure", "Application Insights", "Financial Systems", "NativeScript"], level: 7 },
  { year: 2022, technologies: ["React", "TypeScript", "ERP Integration", "Exact Online"], level: 8 },
  { year: 2023, technologies: ["ElasticSearch", "SEO Analytics", "International Collaboration"], level: 9 },
  { year: 2024, technologies: [".NET Core", "Microservices", "Advanced Performance Optimization"], level: 9 }
];

// Project impact metrics for visualization - Comprehensive project portfolio
export const xpProjectImpacts = [
  {
    project: "Kubio Datasage Platform",
    category: "Financial Technology",
    metrics: {
      performance: 40,
      reliability: 95,
      userSatisfaction: 92,
      codeQuality: 88
    },
    technologies: ["C#", "ASP.NET", "MSSQL", "ElasticSearch"],
    xpColor: xpColorScheme.primary
  },
  {
    project: "Keyword Chef SEO Platform",
    category: "Marketing Technology",
    metrics: {
      performance: 85,
      reliability: 93,
      userSatisfaction: 89,
      codeQuality: 87
    },
    technologies: ["React", ".NET", "JavaScript"],
    xpColor: "#8B5CF6"
  },
  {
    project: "Trafiroad Management System",
    category: "Manufacturing ERP",
    metrics: {
      performance: 78,
      reliability: 96,
      userSatisfaction: 91,
      codeQuality: 85
    },
    technologies: ["C#", "ASP.NET", "Exact Online API"],
    xpColor: "#EF4444"
  },
  {
    project: "Merchant Management Suite",
    category: "Financial Technology",
    metrics: {
      performance: 82,
      reliability: 99,
      userSatisfaction: 94,
      codeQuality: 90
    },
    technologies: ["C#", ".NET", "Azure", "MSSQL"],
    xpColor: xpColorScheme.success
  },
  {
    project: "Hospice Management System",
    category: "Healthcare Technology",
    metrics: {
      performance: 75,
      reliability: 98,
      userSatisfaction: 88,
      codeQuality: 83
    },
    technologies: ["C#", "VB.NET", ".NET Framework", "MSSQL"],
    xpColor: "#059669"
  },
  {
    project: "Customizable ERP System",
    category: "Enterprise Software",
    metrics: {
      performance: 70,
      reliability: 92,
      userSatisfaction: 85,
      codeQuality: 80
    },
    technologies: ["C#", "VB.NET", ".NET Framework", "MSSQL"],
    xpColor: xpColorScheme.warning
  }
];

// Skill categories for radar chart
export const xpSkillCategories = [
  { category: "Backend Development", value: 9, xpColor: xpColorScheme.primary },
  { category: "Frontend Development", value: 8, xpColor: xpColorScheme.secondary },
  { category: "Database Management", value: 8, xpColor: xpColorScheme.warning },
  { category: "DevOps & Tools", value: 7, xpColor: xpColorScheme.success },
  { category: "Project Management", value: 8, xpColor: xpColorScheme.accent },
  { category: "Problem Solving", value: 9, xpColor: xpColorScheme.primary }
];

export default {
  xpColorScheme,
  xpSkillsData,
  xpCareerMilestones,
  xpTechnologyEvolution,
  xpProjectImpacts,
  xpSkillCategories
};
